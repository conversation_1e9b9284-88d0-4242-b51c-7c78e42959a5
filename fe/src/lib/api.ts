import { getApiClient } from "./api/client";
import { Ride, RideRequest, User, ConversationMessage } from "../types";
import axios from 'axios';
import e from "express";

type RideFilters = {
  from?: string;
  to?: string;
  date?: string;
  colleges?: string[];
};

/* Since we don't want to update every attribute of a User, we create a new
type {UserData} for the User attributes that we do want to update. This type is
specific only to this file.  */
type UserData = {
  name?: string;
  email?: string;
  avatar?: string;
  bio?: string;
  preferences?: {
    okayWithChatting: boolean;
    okayWithAnimals: boolean;
    okayWithMusic: boolean;
    okayWithSmoking: boolean;
  };
}

// Get all rides with filters
export async function getAllRides(filters: RideFilters = {}): Promise<Ride[]> {
  try {
    const apiClient = getApiClient();
    const queryParams = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== "") {
        if (key === 'colleges' && Array.isArray(value)) {
          // Handle colleges array - add each college as a separate parameter
          value.forEach(college => {
            queryParams.append('colleges', college);
          });
        } else if (key === 'colleges' && typeof value === 'string') {
          // Handle single college as array
          queryParams.append('colleges', value);
        } else {
          queryParams.append(key, value.toString());
        }
      }
    });

    const response = await apiClient.get(
      `/api/rides/getall?${queryParams.toString()}`
    );


    // Ensure driver data is properly formatted
    const rides = response.data.map((ride: any) => ({
      ...ride,
      driver: {
        ...ride.driver,
        avatar: ride.driver.avatar || null
      }
    }));

    return rides;
  } catch (error) {
    console.error("Failed to fetch rides:", error);
    return [];
  }
}

// Get past rides
export async function getPastRides(): Promise<Ride[]> {
  try {
    const apiClient = getApiClient();
    const response = await apiClient.get(`/api/rides/past`);
    return response.data;
  } catch (error) {
    console.error("Failed to fetch past rides:", error);
    return [];
  }
}

// Create a new ride
export async function createRide(rideData: any): Promise<any> {
  try {
    const apiClient = getApiClient();
    const response = await apiClient.post(`/api/rides`, rideData);
    return response.data;
  } catch (error) {
    console.error("Failed to create ride:", error);
    throw error;
  }
}

// Get user bookings
export async function getUserBookings(userId: string): Promise<any[]> {
  try {
    const apiClient = getApiClient();
    const response = await apiClient.get(`/api/bookings/user/${userId}`);
    return response.data;
  } catch (error) {
    console.error("Failed to fetch user bookings:", error);
    return [];
  }
}

// Get user rides
export async function getUserRides(userId: string): Promise<Ride[]> {
  try {
    const apiClient = getApiClient();
    const response = await apiClient.get(`/api/rides/user/${userId}`);
    return response.data;
  } catch (error) {
    console.error("Failed to fetch user rides:", error);
    return [];
  }
}

// Get user ride requests
export async function getUserRideRequests(): Promise<RideRequest[]> {
  try {
    const apiClient = getApiClient();
    const response = await apiClient.get(`/api/ride-requests/user/me`);
    return response.data;
  } catch (error) {
    console.error("Failed to fetch user ride requests:", error);
    return [];
  }
}

// Get all ride requests with filters
export async function getAllRideRequests(filters: RideFilters = {}): Promise<RideRequest[]> {
  try {
    const apiClient = getApiClient();
    const queryParams = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== "") {
        queryParams.append(key, value.toString());
      }
    });

    const response = await apiClient.get(
      `/api/ride-requests?${queryParams.toString()}`
    );
    return response.data;
  } catch (error) {
    console.error("Failed to fetch ride requests:", error);
    return [];
  }
}

// Create a new ride request for a driver
export async function createRideRequestForDriver(rideData: any): Promise<any> {
  try {
    const apiClient = getApiClient();
    const response = await apiClient.post(`/api/ride-requests`, rideData);
    return response.data;
  } catch (error) {
    console.error("Failed to create ride request:", error);
    throw error;
  }
}

// Delete ride request
export async function deleteRideRequest(requestId: string): Promise<void> {
  try {
    const apiClient = getApiClient();
    await apiClient.delete(`/api/ride-requests/${requestId}`);
  } catch (error) {
    console.error("Failed to delete ride request:", error);
    throw error;
  }
}

//Delete ride post
export async function deleteRidePost(rideId: string): Promise<void> {
  try {
    const apiClient = getApiClient();
    await apiClient.delete(`/api/rides/${rideId}`);
  } catch (error) {
    console.error("Failed to delete posted ride in api:", error)
    throw error;
  }
}

// Book a ride
export async function bookRide(bookingData: {
  rideId: string;
  seats: number;
  suitcases: number;
}): Promise<any> {
  try {
    const apiClient = getApiClient();
    const response = await apiClient.post(`/api/bookings/bookRide`, bookingData);
    return response.data;
  } catch (error) {
    console.error("Failed to book ride:", error);
    throw error;
  }
}

// Get messages for a ride
export async function getRideMessages(rideId: string): Promise<any[]> {
  try {
    const apiClient = getApiClient();
    const response = await apiClient.get(`${import.meta.env.VITE_API_URL}/api/messages/ride/${rideId}`);
    return response.data;
  } catch (error) {
    console.error(`Failed to fetch messages for ride ${rideId}:`, error);
    return [];
  }
}

// Get bookings (passengers) for a ride
export async function getRideBookings(rideId: string): Promise<any[]> {
  try {
    const apiClient = getApiClient();
    const response = await apiClient.get(`/api/bookings/ride/${rideId}`);
    return response.data;
  } catch (error) {
    console.error(`Failed to fetch bookings for ride ${rideId}:`, error);
    return [];
  }
}

// Cancel bookings for a ride(when driver rejects)
export async function updateBookingStatus(bookingId: string, status: string): Promise<any> {
  try {
    const apiClient = getApiClient();
    const response = await apiClient.put(`/api/bookings/${bookingId}/status`, { status });
    return response.data
  } catch (error) {
    console.error(`Failed to change status`, error)
  }
}

export async function updateUserProfile(userData: UserData): Promise<User> {
  try {
    const apiClient = getApiClient();
    console.log({ "userData": userData });
    const response = await apiClient.put('/api/users/profile', userData);
    return response.data;
  } catch (error) {
    console.error('Failed to update profile:', error);
    throw error;
  }
}

// Upload profile image
export async function uploadProfileImage(file: File): Promise<{ avatar: string; user: User }> {
  try {
    const apiClient = getApiClient();
    const formData = new FormData();
    formData.append('image', file);

    // const response = await apiClient.post('/api/users/upload-image', formData, {
    //   headers: {
    //     'Content-Type': 'multipart/form-data',
    //   },
    // });
    const response = await apiClient.post('/api/users/upload-image', formData);
    return response.data;
  } catch (error) {
    console.error('Failed to upload profile image:', error);
    throw error;
  }
}

export async function uploadReviewPhoto(file: File){
  try{
    const apiClient = getApiClient(); 
    const formData = new FormData(); 
    formData.append('photo', file)

    const response = await apiClient.post('/api/users/upload-image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    return response.data; 
  }

  catch(error){
    console.error('Failed to upload photo with review:', error)
    throw error; 
  }
}

// Add this function to fetch a user's profile
export async function getUserProfile(userId: string): Promise<User> {
  try {
    const apiClient = getApiClient();
    const response = await apiClient.get(`/api/users/${userId}`);
    return response.data;
  } catch (error) {
    console.error('Failed to fetch user profile:', error);
    throw error;
  }
}

export async function fetchMessages(rideId: string): Promise<any[]> {
  try {
    const apiClient = getApiClient();
    const response = await apiClient.get(`${import.meta.env.VITE_API_URL}/api/messages/ride/${rideId}`);
    return response.data;
  } catch (error) {
    console.error(`Failed to fetch messages for ride ${rideId}:`, error);
    return [];
  }
}

// Get conversation with a specific user
export async function getConversation(userId: string): Promise<ConversationMessage[]> {
  try {
    const apiClient = getApiClient();
    const response = await apiClient.get(`/api/conversation-messages/conversation/${userId}`);
    return response.data;
  } catch (error) {
    console.error("Failed to fetch conversation:", error);
    throw error;
  }
}

// Send a message to a user
export async function sendConversationMessage(receiverId: string, content: string): Promise<ConversationMessage> {
  try {
    const apiClient = getApiClient();
    const response = await apiClient.post('/api/conversation-messages/send', {
      receiverId,
      content
    });
    return response.data;
  } catch (error) {
    console.error("Failed to send message:", error);
    throw error;
  }
}

// Get pending passenger reviews (using userId as query parameter)
export async function getPendingPassengerReviews(userId: string): Promise<any[]> {
  try {
    const apiClient = getApiClient();
    const response = await apiClient.get(`/api/reviews/getPassengerReviews/${userId}`);
    return response.data;
  } catch (error) {
    console.error("Failed to fetch pending passenger reviews:", error);
    return [];
  }
}

// Get pending driver reviews (using userId as query parameter)
export async function getPendingDriverReviews(userId: string): Promise<any[]> {
  try {
    const apiClient = getApiClient();
    const response = await apiClient.get(`/api/reviews/getDriverReviews/${userId}`);
    return response.data;
  } catch (error) {
    console.error("Failed to fetch pending driver reviews:", error);
    return [];
  }
}

// Submit a review
export async function submitReview(reviewData: {
  bookingId: string;
  stars: number;
  comment: string;
  reviewType: 'driver' | 'passenger';
}): Promise<any> {
  try {
    const apiClient = getApiClient();
    const response = await apiClient.post(`/api/reviews/submit`, reviewData);
    return response.data;
  } catch (error) {
    console.error("Failed to submit review:", error);
    throw error;
  }
}

// Get all conversations for the current user (inbox)
export async function getUserConversations(): Promise<any[]> {
  try {
    const apiClient = getApiClient();
    const response = await apiClient.get(`/api/conversation-messages/inbox`);
    return response.data;
  } catch (error) {
    console.error("Failed to fetch user conversations (inbox):", error);
    return [];
  }
}

// Get user email
export async function getUserEmail(userId: string): Promise<string> {
  try {
    const apiClient = getApiClient();
    console.log("Making API call to: ", apiClient.defaults.baseURL + `/api/users/${userId}/email`);
    const response = await apiClient.get(`/api/users/${userId}/email`);
    console.log("API call response:", response);
    return response.data.email;
  } catch (error) {
    console.error("Failed to fetch user email:", error);
    throw error;
  }
}


export async function getReviewsByReviewee(userId: string): Promise<any[]> {
  try {
    const apiClient = getApiClient();

    const response = await apiClient.get(
      `/api/reviews/${userId}`
    );
    
    return response.data;
  } catch (error) {
    console.error("Failed to fetch ride reviews:", error);
    return [];
  }
}


